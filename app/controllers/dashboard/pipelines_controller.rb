module Dashboard
  class PipelinesController < ApplicationController
    before_action :authenticate_user!
    before_action :set_organization
    before_action :set_pipeline, only: [:show, :edit, :update, :destroy, :run, :schedule, :pause, :logs]

    def index
      @pipelines = @organization.pipelines.includes(:data_source)
      @page_title = "Pipelines"
    end

    def show
      @page_title = @pipeline.name
    end

    def new
      @pipeline = @organization.pipelines.build
      @page_title = "New Pipeline"
    end

    def create
      @pipeline = @organization.pipelines.build(pipeline_params)
      
      if @pipeline.save
        redirect_to dashboard_pipeline_path(@pipeline), notice: 'Pipeline was successfully created.'
      else
        render :new
      end
    end

    def edit
      @page_title = "Edit #{@pipeline.name}"
    end

    def update
      if @pipeline.update(pipeline_params)
        redirect_to dashboard_pipeline_path(@pipeline), notice: 'Pipeline was successfully updated.'
      else
        render :edit
      end
    end

    def destroy
      @pipeline.destroy
      redirect_to dashboard_pipelines_path, notice: 'Pipeline was successfully deleted.'
    end

    def run
      # TODO: Implement pipeline run logic
      redirect_to dashboard_pipeline_path(@pipeline), notice: 'Pipeline run started.'
    end

    def schedule
      # TODO: Implement pipeline scheduling logic
      redirect_to dashboard_pipeline_path(@pipeline), notice: 'Pipeline scheduled.'
    end

    def pause
      # TODO: Implement pipeline pause logic
      redirect_to dashboard_pipeline_path(@pipeline), notice: 'Pipeline paused.'
    end

    def logs
      # TODO: Implement pipeline logs view
      @logs = []
    end

    private

    def set_organization
      @organization = current_user.organization
    end

    def set_pipeline
      @pipeline = @organization.pipelines.find(params[:id])
    end

    def pipeline_params
      params.require(:pipeline).permit(:name, :description, :data_source_id, :schedule, :enabled, :status)
    end
  end
end