<div class="pipeline-detail">
  <div class="page-header mb-6">
    <div class="flex justify-between items-start">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
          <%= @pipeline.name %>
          <span class="px-3 py-1 text-sm font-medium rounded-full bg-<%= pipeline_status_color(@pipeline.status) %>-100 text-<%= pipeline_status_color(@pipeline.status) %>-800">
            <%= @pipeline.status.capitalize %>
          </span>
        </h1>
        <p class="mt-1 text-sm text-gray-600"><%= @pipeline.description %></p>
      </div>
      
      <div class="flex gap-2">
        <% if @pipeline.can_run? %>
          <%= link_to run_dashboard_pipeline_path(@pipeline), method: :post, 
              class: "btn btn-primary", 
              data: { confirm: "Run this pipeline?" } do %>
            <i class="fas fa-play mr-2"></i>Run Now
          <% end %>
        <% end %>
        
        <%= link_to edit_dashboard_pipeline_path(@pipeline), class: "btn btn-secondary" do %>
          <i class="fas fa-edit mr-2"></i>Edit
        <% end %>
        
        <%= link_to dashboard_pipeline_path(@pipeline), method: :delete, 
            class: "btn btn-danger", 
            data: { confirm: "Are you sure you want to delete this pipeline?" } do %>
          <i class="fas fa-trash mr-2"></i>Delete
        <% end %>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Pipeline Information -->
    <div class="lg:col-span-2 space-y-6">
      <div class="bg-white shadow-sm rounded-lg p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Pipeline Details</h2>
        
        <dl class="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <dt class="text-sm font-medium text-gray-500">Data Source</dt>
            <dd class="mt-1 text-sm text-gray-900">
              <%= link_to @pipeline.data_source.name, dashboard_data_source_path(@pipeline.data_source), 
                  class: "text-primary-600 hover:text-primary-700" %>
            </dd>
          </div>
          
          <div>
            <dt class="text-sm font-medium text-gray-500">Schedule</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @pipeline.schedule.capitalize %></dd>
          </div>
          
          <div>
            <dt class="text-sm font-medium text-gray-500">Created</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @pipeline.created_at.strftime("%B %d, %Y") %></dd>
          </div>
          
          <div>
            <dt class="text-sm font-medium text-gray-500">Last Run</dt>
            <dd class="mt-1 text-sm text-gray-900">
              <% if @pipeline.last_run_at %>
                <%= time_ago_in_words(@pipeline.last_run_at) %> ago
              <% else %>
                Never
              <% end %>
            </dd>
          </div>
        </dl>

        <% if @pipeline.error_message.present? %>
          <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <h3 class="text-sm font-medium text-red-800">Last Error</h3>
            <p class="mt-1 text-sm text-red-700"><%= @pipeline.error_message %></p>
          </div>
        <% end %>
      </div>

      <!-- Pipeline Steps -->
      <div class="bg-white shadow-sm rounded-lg p-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold text-gray-900">Pipeline Steps</h2>
          <%= link_to new_dashboard_pipeline_pipeline_step_path(@pipeline), 
              class: "text-sm text-primary-600 hover:text-primary-700" do %>
            <i class="fas fa-plus mr-1"></i>Add Step
          <% end %>
        </div>
        
        <% if @pipeline.pipeline_steps.any? %>
          <div class="space-y-3">
            <% @pipeline.pipeline_steps.order(:position).each do |step| %>
              <div class="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors">
                <div class="flex justify-between items-start">
                  <div>
                    <h3 class="font-medium text-gray-900"><%= step.name %></h3>
                    <p class="text-sm text-gray-600 mt-1"><%= step.step_type.humanize %></p>
                  </div>
                  <div class="flex gap-2">
                    <%= link_to edit_dashboard_pipeline_pipeline_step_path(@pipeline, step), 
                        class: "text-gray-400 hover:text-gray-600" do %>
                      <i class="fas fa-edit"></i>
                    <% end %>
                    <%= link_to dashboard_pipeline_pipeline_step_path(@pipeline, step), 
                        method: :delete,
                        data: { confirm: "Delete this step?" },
                        class: "text-gray-400 hover:text-red-600" do %>
                      <i class="fas fa-trash"></i>
                    <% end %>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <p class="text-gray-500 text-center py-8">No steps configured yet</p>
        <% end %>
      </div>

      <!-- Recent Runs -->
      <div class="bg-white shadow-sm rounded-lg p-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold text-gray-900">Recent Runs</h2>
          <%= link_to logs_dashboard_pipeline_path(@pipeline), 
              class: "text-sm text-primary-600 hover:text-primary-700" do %>
            View All Logs
          <% end %>
        </div>
        
        <% if @pipeline.pipeline_runs.any? %>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead>
                <tr>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Started At
                  </th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Duration
                  </th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Records
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <% @pipeline.pipeline_runs.recent.limit(5).each do |run| %>
                  <tr>
                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                      <%= run.started_at.strftime("%m/%d/%Y %I:%M %p") %>
                    </td>
                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                      <% if run.completed_at %>
                        <%= distance_of_time_in_words(run.started_at, run.completed_at) %>
                      <% else %>
                        -
                      <% end %>
                    </td>
                    <td class="px-3 py-4 whitespace-nowrap">
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                   bg-<%= run_status_color(run.status) %>-100 
                                   text-<%= run_status_color(run.status) %>-800">
                        <%= run.status.capitalize %>
                      </span>
                    </td>
                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                      <%= number_with_delimiter(run.records_processed || 0) %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        <% else %>
          <p class="text-gray-500 text-center py-8">No runs yet</p>
        <% end %>
      </div>
    </div>

    <!-- Sidebar -->
    <div class="space-y-6">
      <!-- Quick Actions -->
      <div class="bg-white shadow-sm rounded-lg p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        
        <div class="space-y-3">
          <% if @pipeline.status == 'active' %>
            <%= link_to pause_dashboard_pipeline_path(@pipeline), method: :post,
                class: "w-full btn btn-outline-secondary" do %>
              <i class="fas fa-pause mr-2"></i>Pause Pipeline
            <% end %>
          <% else %>
            <%= link_to schedule_dashboard_pipeline_path(@pipeline), method: :post,
                class: "w-full btn btn-outline-primary" do %>
              <i class="fas fa-clock mr-2"></i>Activate Pipeline
            <% end %>
          <% end %>
          
          <%= link_to logs_dashboard_pipeline_path(@pipeline), 
              class: "w-full btn btn-outline-secondary" do %>
            <i class="fas fa-file-alt mr-2"></i>View Logs
          <% end %>
        </div>
      </div>

      <!-- Performance Metrics -->
      <div class="bg-white shadow-sm rounded-lg p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Performance</h2>
        
        <div class="space-y-4">
          <div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-500">Success Rate</span>
              <span class="font-medium text-gray-900">95%</span>
            </div>
            <div class="mt-1 w-full bg-gray-200 rounded-full h-2">
              <div class="bg-green-600 h-2 rounded-full" style="width: 95%"></div>
            </div>
          </div>
          
          <div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-500">Avg Duration</span>
              <span class="font-medium text-gray-900">2m 34s</span>
            </div>
          </div>
          
          <div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-500">Total Records</span>
              <span class="font-medium text-gray-900">1.2M</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Next Scheduled Run -->
      <% if @pipeline.scheduled? %>
        <div class="bg-white shadow-sm rounded-lg p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Next Run</h2>
          <p class="text-sm text-gray-600">
            Scheduled for <%= @pipeline.next_scheduled_run_time&.strftime("%B %d at %I:%M %p") %>
          </p>
        </div>
      <% end %>
    </div>
  </div>
</div>

<style>
  .btn {
    @apply inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 border-transparent focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply bg-white text-gray-700 border-gray-300 hover:bg-gray-50 focus:ring-primary-500;
  }
  
  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 border-transparent focus:ring-red-500;
  }
  
  .btn-outline-primary {
    @apply bg-white text-primary-600 border-primary-600 hover:bg-primary-50 focus:ring-primary-500;
  }
  
  .btn-outline-secondary {
    @apply bg-white text-gray-600 border-gray-300 hover:bg-gray-50 focus:ring-gray-500;
  }
</style>