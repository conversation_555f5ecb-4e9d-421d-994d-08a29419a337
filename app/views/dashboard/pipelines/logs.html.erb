<div class="logs-page">
  <div class="page-header mb-6">
    <div class="flex items-center gap-4">
      <%= link_to dashboard_pipeline_path(@pipeline), class: "text-gray-500 hover:text-gray-700" do %>
        <i class="fas fa-arrow-left"></i>
      <% end %>
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Pipeline Logs</h1>
        <p class="mt-1 text-sm text-gray-600"><%= @pipeline.name %></p>
      </div>
    </div>
  </div>

  <div class="bg-white shadow-sm rounded-lg">
    <div class="p-6 border-b border-gray-200">
      <div class="flex justify-between items-center">
        <h2 class="text-lg font-semibold text-gray-900">Execution History</h2>
        <div class="flex gap-2">
          <select class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-primary-500 focus:border-primary-500">
            <option>All Statuses</option>
            <option>Completed</option>
            <option>Failed</option>
            <option>Running</option>
          </select>
          <input type="date" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-primary-500 focus:border-primary-500" />
        </div>
      </div>
    </div>

    <% if @logs && @logs.any? %>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Run ID
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Started At
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Duration
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Records
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% @logs.each do |log| %>
              <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  #<%= log.id %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= log.started_at.strftime("%m/%d/%Y %I:%M %p") %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <% if log.completed_at %>
                    <%= distance_of_time_in_words(log.started_at, log.completed_at) %>
                  <% else %>
                    -
                  <% end %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                               bg-<%= run_status_color(log.status) %>-100 
                               text-<%= run_status_color(log.status) %>-800">
                    <%= log.status.capitalize %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= number_with_delimiter(log.records_processed || 0) %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button class="text-primary-600 hover:text-primary-900">View Details</button>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
      
      <div class="px-6 py-4 border-t border-gray-200">
        <div class="flex justify-between items-center">
          <p class="text-sm text-gray-700">
            Showing <span class="font-medium">1</span> to <span class="font-medium">10</span> of <span class="font-medium">97</span> results
          </p>
          <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
              <i class="fas fa-chevron-left"></i>
            </a>
            <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
              1
            </a>
            <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
              2
            </a>
            <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
              3
            </a>
            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
              <i class="fas fa-chevron-right"></i>
            </a>
          </nav>
        </div>
      </div>
    <% else %>
      <div class="text-center py-12">
        <i class="fas fa-file-alt text-4xl text-gray-300 mb-4"></i>
        <p class="text-gray-500">No execution logs available</p>
      </div>
    <% end %>
  </div>
</div>