<div class="max-w-4xl mx-auto">
  <div class="page-header mb-6">
    <h1 class="text-2xl font-bold text-gray-900">Edit Pipeline</h1>
    <p class="mt-1 text-sm text-gray-600">Update pipeline configuration</p>
  </div>

  <div class="bg-white shadow-sm rounded-lg p-6">
    <%= form_with(model: [:dashboard, @pipeline], local: true) do |form| %>
      <% if @pipeline.errors.any? %>
        <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-6">
          <h3 class="font-medium mb-2">Please fix the following errors:</h3>
          <ul class="list-disc list-inside">
            <% @pipeline.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      <% end %>

      <div class="space-y-6">
        <div>
          <%= form.label :name, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.text_field :name, class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" %>
        </div>

        <div>
          <%= form.label :description, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.text_area :description, rows: 3, class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" %>
        </div>

        <div>
          <%= form.label :data_source_id, "Data Source", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.select :data_source_id, 
              options_for_select(@organization.data_sources.pluck(:name, :id), @pipeline.data_source_id), 
              { prompt: "Select a data source..." }, 
              class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" %>
        </div>

        <div>
          <%= form.label :schedule, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.select :schedule, 
              options_for_select([
                ["Manual - Run on demand", "manual"],
                ["Hourly - Every hour", "hourly"],
                ["Daily - Once per day", "daily"],
                ["Weekly - Once per week", "weekly"],
                ["Monthly - Once per month", "monthly"]
              ], @pipeline.schedule), 
              {}, 
              class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" %>
        </div>

        <div>
          <%= form.label :status, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.select :status, 
              options_for_select(Pipeline::STATUSES.map { |s| [s.capitalize, s] }, @pipeline.status), 
              {}, 
              class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" %>
        </div>

        <div class="flex items-center">
          <%= form.check_box :enabled, class: "h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" %>
          <%= form.label :enabled, "Pipeline enabled", class: "ml-2 block text-sm text-gray-700" %>
        </div>
      </div>

      <div class="mt-6 flex justify-end space-x-3">
        <%= link_to "Cancel", dashboard_pipeline_path(@pipeline), class: "px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500" %>
        <%= form.submit "Update Pipeline", class: "px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500" %>
      </div>
    <% end %>
  </div>
</div>