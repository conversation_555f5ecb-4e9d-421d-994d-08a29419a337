module PipelinesHelper
  def pipeline_status_color(status)
    case status
    when 'active', 'running'
      'green'
    when 'paused', 'scheduled'
      'yellow'
    when 'failed'
      'red'
    when 'inactive'
      'gray'
    else
      'gray'
    end
  end
  
  def pipeline_status_icon(status)
    case status
    when 'active'
      'fas fa-check-circle'
    when 'running'
      'fas fa-spinner fa-spin'
    when 'paused'
      'fas fa-pause-circle'
    when 'scheduled'
      'fas fa-clock'
    when 'failed'
      'fas fa-exclamation-circle'
    when 'inactive'
      'fas fa-times-circle'
    else
      'fas fa-question-circle'
    end
  end
  
  def run_status_color(status)
    case status
    when 'completed', 'success'
      'green'
    when 'running', 'pending'
      'yellow'
    when 'failed', 'error'
      'red'
    else
      'gray'
    end
  end
end